plugins {
	id 'com.gradle.plugin-publish' version '1.0.0'
}

group = 'io.github.scm4j'
version = file('version').text.trim()

repositories {
	mavenCentral()
}

dependencies {
	testImplementation 'junit:junit:4.13.2'
	testImplementation 'org.hamcrest:hamcrest-all:1.3'
}

java {
	sourceCompatibility = JavaVersion.VERSION_1_8
	targetCompatibility = JavaVersion.VERSION_1_8

	withJavadocJar()
	withSourcesJar()
}

jar {
	manifest.attributes (
		'Specification-Title': project.name,
		'Specification-Version': project.version.replaceAll(/-SNAPSHOT$/, ''),
		'Implementation-Title': project.name,
		'Implementation-Version': "${project.version} (${new Date().format('yyyy-MM-dd')})",
	)
}

test.testLogging.exceptionFormat 'full'

test {
	useJUnit {
		excludeCategories 'io.github.scm4j.releaser.gradle.IntegrationTest'
	}
}

pluginBundle {
	website = 'https://github.com/scm4j/scm4j-releaser-gradle-plugin'
	vcsUrl = 'https://github.com/scm4j/scm4j-releaser-gradle-plugin.git'
	tags = ['scm', 'release-automation', 'release-helper', 'dependencies']
}

gradlePlugin {
	plugins {
		scm4jReleaserGradlePlugin {
			id = 'io.github.scm4j.scm4j-releaser-gradle-plugin'
			displayName = 'scm4j-releaser Gradle plugin'
			description = 'This plugin loads version and managable dependencies from external files (version and mdeps)'
			implementationClass = 'io.github.scm4j.releaser.gradle.ReleaserGradlePlugin'
		}
	}
}
